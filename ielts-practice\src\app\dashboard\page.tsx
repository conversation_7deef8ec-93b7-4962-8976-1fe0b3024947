"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Navigation } from "@/components/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Clock, TrendingUp, Award } from "lucide-react"
import Link from "next/link"

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return
    if (!session) {
      router.push("/auth/signin")
    }
  }, [session, status, router])

  if (status === "loading") {
    return <div>Loading...</div>
  }

  if (!session) {
    return null
  }

  // Mock data for demonstration
  const mockStats = {
    totalTests: 12,
    completedTests: 8,
    averageScore: 7.5,
    recentTests: [
      { id: 1, title: "IELTS Reading Practice Test 1", score: 8.0, date: "2024-01-15", type: "READING" },
      { id: 2, title: "IELTS Writing Task 1", score: 7.0, date: "2024-01-14", type: "WRITING" },
      { id: 3, title: "IELTS Listening Test", score: 8.5, date: "2024-01-13", type: "LISTENING" },
    ]
  }

  const completionRate = (mockStats.completedTests / mockStats.totalTests) * 100

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Welcome back, {session.user.name || session.user.email}!</h1>
          <p className="text-muted-foreground">Track your IELTS preparation progress and continue practicing.</p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.totalTests}</div>
              <p className="text-xs text-muted-foreground">Available for practice</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.completedTests}</div>
              <p className="text-xs text-muted-foreground">Tests completed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.averageScore}</div>
              <p className="text-xs text-muted-foreground">Out of 9.0</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{completionRate.toFixed(0)}%</div>
              <Progress value={completionRate} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Test Results</CardTitle>
              <CardDescription>Your latest test performances</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockStats.recentTests.map((test) => (
                  <div key={test.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{test.title}</h4>
                      <p className="text-sm text-muted-foreground">{test.date}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">{test.type}</Badge>
                      <div className="text-lg font-bold">{test.score}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Continue your IELTS preparation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Link href="/tests?type=reading">
                  <Button variant="outline" className="w-full h-20 flex flex-col">
                    <BookOpen className="h-6 w-6 mb-2" />
                    Reading
                  </Button>
                </Link>
                <Link href="/tests?type=writing">
                  <Button variant="outline" className="w-full h-20 flex flex-col">
                    <BookOpen className="h-6 w-6 mb-2" />
                    Writing
                  </Button>
                </Link>
                <Link href="/tests?type=listening">
                  <Button variant="outline" className="w-full h-20 flex flex-col">
                    <BookOpen className="h-6 w-6 mb-2" />
                    Listening
                  </Button>
                </Link>
                <Link href="/tests?type=speaking">
                  <Button variant="outline" className="w-full h-20 flex flex-col">
                    <BookOpen className="h-6 w-6 mb-2" />
                    Speaking
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
