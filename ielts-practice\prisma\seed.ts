import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create a teacher user
  const hashedPassword = await bcrypt.hash('password123', 12)
  
  const teacher = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: 'TEACHER',
    },
  })

  // Create a student user
  const student = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: 'STUDENT',
    },
  })

  // Create a sample reading test
  const readingTest = await prisma.test.create({
    data: {
      title: 'IELTS Reading Practice Test 1',
      description: 'A comprehensive reading test covering various question types',
      type: 'READING',
      difficulty: 'INTERMEDIATE',
      duration: 60,
      createdBy: teacher.id,
      questions: {
        create: [
          {
            type: 'MULTIPLE_CHOICE',
            content: 'What is the main idea of the passage?',
            options: [
              'Climate change is not real',
              'Climate change affects global weather patterns',
              'Weather is unpredictable',
              'Scientists disagree about climate'
            ],
            correctAnswer: 'Climate change affects global weather patterns',
            points: 1,
            order: 1,
          },
          {
            type: 'TRUE_FALSE',
            content: 'The passage states that renewable energy is becoming more affordable.',
            correctAnswer: true,
            points: 1,
            order: 2,
          },
          {
            type: 'FILL_IN_BLANK',
            content: 'Solar panels convert _______ into electricity.',
            correctAnswer: 'sunlight',
            points: 1,
            order: 3,
          },
        ],
      },
    },
  })

  // Create a sample writing test
  const writingTest = await prisma.test.create({
    data: {
      title: 'IELTS Writing Task 1 - Academic',
      description: 'Describe the chart showing renewable energy usage',
      type: 'WRITING',
      difficulty: 'INTERMEDIATE',
      duration: 20,
      createdBy: teacher.id,
      questions: {
        create: [
          {
            type: 'ESSAY',
            content: 'The chart below shows the percentage of renewable energy usage in different countries from 2010 to 2020. Summarize the information by selecting and reporting the main features, and make comparisons where relevant.',
            correctAnswer: 'Sample answer focusing on trends and comparisons',
            points: 9,
            order: 1,
            imageUrl: '/charts/renewable-energy.png',
          },
        ],
      },
    },
  })

  // Create a sample listening test
  const listeningTest = await prisma.test.create({
    data: {
      title: 'IELTS Listening Practice Test 1',
      description: 'A listening test with various question types',
      type: 'LISTENING',
      difficulty: 'INTERMEDIATE',
      duration: 30,
      createdBy: teacher.id,
      questions: {
        create: [
          {
            type: 'MULTIPLE_CHOICE',
            content: 'What time does the library close on weekdays?',
            options: ['5:00 PM', '6:00 PM', '7:00 PM', '8:00 PM'],
            correctAnswer: '7:00 PM',
            points: 1,
            order: 1,
            audioUrl: '/audio/library-hours.mp3',
          },
          {
            type: 'FILL_IN_BLANK',
            content: 'The speaker mentions that the new policy will be implemented in _______.',
            correctAnswer: 'September',
            points: 1,
            order: 2,
            audioUrl: '/audio/policy-announcement.mp3',
          },
        ],
      },
    },
  })

  // Create sample progress for the student
  await prisma.userProgress.createMany({
    data: [
      {
        userId: student.id,
        testType: 'READING',
        totalTests: 5,
        completedTests: 3,
        averageScore: 7.5,
        bestScore: 8.5,
        lastTestDate: new Date('2024-01-15'),
      },
      {
        userId: student.id,
        testType: 'WRITING',
        totalTests: 3,
        completedTests: 2,
        averageScore: 6.5,
        bestScore: 7.0,
        lastTestDate: new Date('2024-01-14'),
      },
      {
        userId: student.id,
        testType: 'LISTENING',
        totalTests: 4,
        completedTests: 3,
        averageScore: 8.0,
        bestScore: 8.5,
        lastTestDate: new Date('2024-01-13'),
      },
    ],
  })

  console.log('✅ Database seeded successfully!')
  console.log(`👨‍🏫 Teacher: ${teacher.email} (password: password123)`)
  console.log(`👨‍🎓 Student: ${student.email} (password: password123)`)
  console.log(`📚 Created ${3} sample tests`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
