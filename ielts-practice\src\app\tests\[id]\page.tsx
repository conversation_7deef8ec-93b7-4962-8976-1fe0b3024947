"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter, useParams } from "next/navigation"
import { Navigation } from "@/components/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Clock, CheckCircle, AlertCircle } from "lucide-react"

interface Question {
  id: string
  type: string
  content: string
  options?: string[]
  correctAnswer: any
  points: number
  order: number
  audioUrl?: string
  imageUrl?: string
}

interface Test {
  id: string
  title: string
  description: string | null
  type: string
  difficulty: string
  duration: number
  questions: Question[]
}

export default function TestPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const testId = params.id as string

  const [test, setTest] = useState<Test | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [timeLeft, setTimeLeft] = useState(0)
  const [isSubmitted, setIsSubmitted] = useState(false)

  useEffect(() => {
    if (status === "loading") return
    if (!session) {
      router.push("/auth/signin")
      return
    }

    // Mock test data - in a real app, this would fetch from API
    const mockTest: Test = {
      id: testId,
      title: 'IELTS Reading Practice Test 1',
      description: 'A comprehensive reading test covering various question types',
      type: 'READING',
      difficulty: 'INTERMEDIATE',
      duration: 60,
      questions: [
        {
          id: '1',
          type: 'MULTIPLE_CHOICE',
          content: 'What is the main idea of the passage about climate change?',
          options: [
            'Climate change is not real',
            'Climate change affects global weather patterns',
            'Weather is unpredictable',
            'Scientists disagree about climate'
          ],
          correctAnswer: 'Climate change affects global weather patterns',
          points: 1,
          order: 1,
        },
        {
          id: '2',
          type: 'TRUE_FALSE',
          content: 'The passage states that renewable energy is becoming more affordable.',
          correctAnswer: true,
          points: 1,
          order: 2,
        },
        {
          id: '3',
          type: 'FILL_IN_BLANK',
          content: 'Solar panels convert _______ into electricity.',
          correctAnswer: 'sunlight',
          points: 1,
          order: 3,
        },
      ]
    }

    setTest(mockTest)
    setTimeLeft(mockTest.duration * 60) // Convert minutes to seconds
    setLoading(false)
  }, [session, status, router, testId])

  // Timer effect
  useEffect(() => {
    if (timeLeft > 0 && !isSubmitted) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    } else if (timeLeft === 0 && !isSubmitted) {
      handleSubmit()
    }
  }, [timeLeft, isSubmitted])

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }))
  }

  const handleSubmit = () => {
    setIsSubmitted(true)
    // In a real app, this would submit to API
    console.log('Submitted answers:', answers)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const renderQuestion = (question: Question) => {
    const answer = answers[question.id]

    switch (question.type) {
      case 'MULTIPLE_CHOICE':
        return (
          <RadioGroup
            value={answer || ''}
            onValueChange={(value) => handleAnswerChange(question.id, value)}
            disabled={isSubmitted}
          >
            {question.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${question.id}-${index}`} />
                <Label htmlFor={`${question.id}-${index}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        )

      case 'TRUE_FALSE':
        return (
          <RadioGroup
            value={answer?.toString() || ''}
            onValueChange={(value) => handleAnswerChange(question.id, value === 'true')}
            disabled={isSubmitted}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true" id={`${question.id}-true`} />
              <Label htmlFor={`${question.id}-true`}>True</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="false" id={`${question.id}-false`} />
              <Label htmlFor={`${question.id}-false`}>False</Label>
            </div>
          </RadioGroup>
        )

      case 'FILL_IN_BLANK':
        return (
          <Input
            value={answer || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Enter your answer"
            disabled={isSubmitted}
          />
        )

      case 'ESSAY':
        return (
          <Textarea
            value={answer || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Write your essay here..."
            rows={10}
            disabled={isSubmitted}
          />
        )

      default:
        return <div>Unsupported question type</div>
    }
  }

  if (status === "loading" || loading) {
    return <div>Loading...</div>
  }

  if (!session || !test) {
    return null
  }

  const progress = ((currentQuestion + 1) / test.questions.length) * 100

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Test Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">{test.title}</h1>
              <p className="text-muted-foreground">{test.description}</p>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-5 w-5" />
                <span className={`text-lg font-mono ${timeLeft < 300 ? 'text-red-600' : ''}`}>
                  {formatTime(timeLeft)}
                </span>
              </div>
              <Badge variant={timeLeft < 300 ? 'destructive' : 'secondary'}>
                {test.type} • {test.difficulty}
              </Badge>
            </div>
          </div>
          
          <Progress value={progress} className="mb-4" />
          <p className="text-sm text-muted-foreground">
            Question {currentQuestion + 1} of {test.questions.length}
          </p>
        </div>

        {/* Question */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Question {currentQuestion + 1}</span>
              <Badge variant="outline">{test.questions[currentQuestion].points} point(s)</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <p className="text-lg mb-4">{test.questions[currentQuestion].content}</p>
              {renderQuestion(test.questions[currentQuestion])}
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
            disabled={currentQuestion === 0 || isSubmitted}
          >
            Previous
          </Button>
          
          <div className="space-x-2">
            {currentQuestion < test.questions.length - 1 ? (
              <Button
                onClick={() => setCurrentQuestion(currentQuestion + 1)}
                disabled={isSubmitted}
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitted}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitted ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submitted
                  </>
                ) : (
                  'Submit Test'
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Submission Confirmation */}
        {isSubmitted && (
          <Card className="mt-8 border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">Test submitted successfully!</span>
              </div>
              <p className="text-green-700 mt-2">
                Your answers have been recorded. You can view your results in the dashboard.
              </p>
              <Button className="mt-4" onClick={() => router.push('/dashboard')}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
