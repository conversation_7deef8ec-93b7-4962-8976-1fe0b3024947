import { NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
// import { db } from "@/lib/db"

export async function POST(req: NextRequest) {
  try {
    const { email, password, name, role = "STUDENT" } = await req.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      )
    }

    // Temporary mock registration for testing
    if (email === "<EMAIL>" || email === "<EMAIL>") {
      return NextResponse.json(
        { error: "User already exists" },
        { status: 400 }
      )
    }

    // Mock successful registration
    const user = {
      id: Math.random().toString(),
      email,
      name,
      role,
    }

    return NextResponse.json(
      { user },
      { status: 201 }
    )
  } catch (error) {
    console.error("Registration error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
