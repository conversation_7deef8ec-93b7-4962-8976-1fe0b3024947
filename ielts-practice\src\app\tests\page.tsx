"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Navigation } from "@/components/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Clock, BookOpen, Users, Star } from "lucide-react"
import Link from "next/link"

interface Test {
  id: string
  title: string
  description: string | null
  type: string
  difficulty: string
  duration: number
  _count: {
    questions: number
    submissions: number
  }
}

export default function TestsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [tests, setTests] = useState<Test[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState(searchParams.get('type') || 'ALL')

  useEffect(() => {
    if (status === "loading") return
    if (!session) {
      router.push("/auth/signin")
      return
    }

    // Mock data for now - in a real app, this would fetch from API
    const mockTests: Test[] = [
      {
        id: '1',
        title: 'IELTS Reading Practice Test 1',
        description: 'A comprehensive reading test covering various question types',
        type: 'READING',
        difficulty: 'INTERMEDIATE',
        duration: 60,
        _count: { questions: 3, submissions: 15 }
      },
      {
        id: '2',
        title: 'IELTS Writing Task 1 - Academic',
        description: 'Describe the chart showing renewable energy usage',
        type: 'WRITING',
        difficulty: 'INTERMEDIATE',
        duration: 20,
        _count: { questions: 1, submissions: 8 }
      },
      {
        id: '3',
        title: 'IELTS Listening Practice Test 1',
        description: 'A listening test with various question types',
        type: 'LISTENING',
        difficulty: 'INTERMEDIATE',
        duration: 30,
        _count: { questions: 2, submissions: 12 }
      },
      {
        id: '4',
        title: 'IELTS Reading Practice Test 2',
        description: 'Advanced reading comprehension with academic texts',
        type: 'READING',
        difficulty: 'ADVANCED',
        duration: 60,
        _count: { questions: 4, submissions: 6 }
      },
      {
        id: '5',
        title: 'IELTS Writing Task 2 - Opinion Essay',
        description: 'Express your opinion on contemporary social issues',
        type: 'WRITING',
        difficulty: 'ADVANCED',
        duration: 40,
        _count: { questions: 1, submissions: 10 }
      }
    ]

    setTests(mockTests)
    setLoading(false)
  }, [session, status, router])

  if (status === "loading" || loading) {
    return <div>Loading...</div>
  }

  if (!session) {
    return null
  }

  const filteredTests = filter === 'ALL' 
    ? tests 
    : tests.filter(test => test.type === filter)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'BEGINNER': return 'bg-green-100 text-green-800'
      case 'INTERMEDIATE': return 'bg-yellow-100 text-yellow-800'
      case 'ADVANCED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'READING': return 'bg-blue-100 text-blue-800'
      case 'WRITING': return 'bg-purple-100 text-purple-800'
      case 'LISTENING': return 'bg-orange-100 text-orange-800'
      case 'SPEAKING': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">IELTS Practice Tests</h1>
          <p className="text-muted-foreground">Choose from our collection of practice tests to improve your IELTS skills.</p>
        </div>

        {/* Filter */}
        <div className="mb-6">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Tests</SelectItem>
              <SelectItem value="READING">Reading</SelectItem>
              <SelectItem value="WRITING">Writing</SelectItem>
              <SelectItem value="LISTENING">Listening</SelectItem>
              <SelectItem value="SPEAKING">Speaking</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tests Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTests.map((test) => (
            <Card key={test.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start mb-2">
                  <Badge className={getTypeColor(test.type)}>
                    {test.type}
                  </Badge>
                  <Badge variant="outline" className={getDifficultyColor(test.difficulty)}>
                    {test.difficulty}
                  </Badge>
                </div>
                <CardTitle className="text-lg">{test.title}</CardTitle>
                <CardDescription>{test.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {test.duration} min
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-1" />
                    {test._count.questions} questions
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {test._count.submissions} taken
                  </div>
                </div>
                <Link href={`/tests/${test.id}`}>
                  <Button className="w-full">
                    Start Test
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTests.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No tests found</h3>
            <p className="text-muted-foreground">Try adjusting your filter or check back later for new tests.</p>
          </div>
        )}
      </div>
    </div>
  )
}
