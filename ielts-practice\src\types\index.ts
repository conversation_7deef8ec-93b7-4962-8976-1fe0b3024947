import { User, Test, Question, TestSubmission, UserProgress } from '@prisma/client'

export type UserRole = 'STUDENT' | 'TEACHER' | 'ADMIN'
export type TestType = 'WRITING' | 'READING' | 'LISTENING' | 'SPEAKING'
export type QuestionType = 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'FILL_IN_BLANK' | 'ESSAY' | 'SHORT_ANSWER' | 'MATCHING' | 'ORDERING'
export type Difficulty = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
export type SubmissionStatus = 'IN_PROGRESS' | 'COMPLETED' | 'ABANDONED'

export interface ExtendedUser extends User {
  role: UserRole
}

export interface TestWithQuestions extends Test {
  questions: Question[]
  _count?: {
    submissions: number
  }
}

export interface SubmissionWithAnswers extends TestSubmission {
  answers: {
    id: string
    questionId: string
    answer: any
    isCorrect: boolean | null
    points: number | null
  }[]
  test: {
    title: string
    type: TestType
  }
}

export interface UserProgressWithDetails extends UserProgress {
  user: {
    name: string | null
    email: string
  }
}

export interface DashboardStats {
  totalTests: number
  completedTests: number
  averageScore: number
  recentSubmissions: SubmissionWithAnswers[]
}

export interface TeacherDashboardStats {
  totalStudents: number
  totalTests: number
  totalSubmissions: number
  averageClassScore: number
  recentActivity: {
    studentName: string
    testTitle: string
    score: number
    completedAt: Date
  }[]
}

// Form types
export interface CreateTestForm {
  title: string
  description?: string
  type: TestType
  difficulty: Difficulty
  duration: number
  questions: CreateQuestionForm[]
}

export interface CreateQuestionForm {
  type: QuestionType
  content: string
  options?: string[]
  correctAnswer: any
  points: number
  audioUrl?: string
  imageUrl?: string
}

export interface SubmitAnswerForm {
  questionId: string
  answer: any
}
