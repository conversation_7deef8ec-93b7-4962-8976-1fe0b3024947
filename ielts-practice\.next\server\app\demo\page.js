/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/demo/page";
exports.ids = ["app/demo/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'demo',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/demo/page\",\n        pathname: \"/demo\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(rsc)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZW1vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJRUxUUyBwcmFjdGljZVxcXFxpZWx0cy1wcmFjdGljZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxJRUxUUyBwcmFjdGljZVxcaWVsdHMtcHJhY3RpY2VcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\IELTS practice\\ielts-practice\\src\\app\\demo\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"03f52d364998\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSUVMVFMgcHJhY3RpY2VcXGllbHRzLXByYWN0aWNlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwM2Y1MmQzNjQ5OThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"IELTS Practice Platform\",\n    description: \"Practice IELTS tests with comprehensive tracking and analytics\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\IELTS practice\\ielts-practice\\src\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNXaW5kb3dzJTIwMTElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDSUVMVFMlMjBwcmFjdGljZSU1QyU1Q2llbHRzLXByYWN0aWNlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNXaW5kb3dzJTIwMTElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDSUVMVFMlMjBwcmFjdGljZSU1QyU1Q2llbHRzLXByYWN0aWNlJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDV2luZG93cyUyMDExJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0lFTFRTJTIwcHJhY3RpY2UlNUMlNUNpZWx0cy1wcmFjdGljZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0wiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFdpbmRvd3MgMTFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcSUVMVFMgcHJhY3RpY2VcXFxcaWVsdHMtcHJhY3RpY2VcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(ssr)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJRUxUUyUyMHByYWN0aWNlJTVDJTVDaWVsdHMtcHJhY3RpY2UlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZW1vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJRUxUUyBwcmFjdGljZVxcXFxpZWx0cy1wcmFjdGljZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CIELTS%20practice%5C%5Cielts-practice%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_inspera_reading_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/inspera-reading-interface */ \"(ssr)/./src/components/inspera-reading-interface.tsx\");\n/* harmony import */ var _data_authentic_reading_test__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/authentic-reading-test */ \"(ssr)/./src/data/authentic-reading-test.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DemoPage() {\n    const [showTest, setShowTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleAnswerChange = (questionId, answer)=>{\n        setAnswers((prev)=>({\n                ...prev,\n                [questionId]: answer\n            }));\n    };\n    const handleSubmit = ()=>{\n        console.log(\"Test submitted with answers:\", answers);\n        alert(\"Test submitted! Check console for answers.\");\n    };\n    if (showTest) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inspera_reading_interface__WEBPACK_IMPORTED_MODULE_2__.InspecraReadingInterface, {\n            testTakerId: \"12345678\",\n            parts: _data_authentic_reading_test__WEBPACK_IMPORTED_MODULE_3__.authenticReadingTest.parts,\n            answers: answers,\n            onAnswerChange: handleAnswerChange,\n            onSubmit: handleSubmit\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Authentic IELTS Reading Test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-8\",\n                            children: \"Experience the exact Inspera interface used in official IELTS test centers worldwide. This demo replicates every detail of the real test environment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                            children: \"Test Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Duration:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"60 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Questions:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"40 questions (33 shown in demo)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Parts:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"3 reading passages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Question Types:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"True/False/Not Given, Multiple Choice, Fill in Blanks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Interface:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 font-semibold\",\n                                            children: \"Official Inspera Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Authentic Features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Exact IELTS logo and header design\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Split-screen layout: passage left, questions right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Official question numbering and navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Authentic radio button styling\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Real Cambridge IELTS content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Part-by-part navigation system\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Inspera-style bottom navigation bar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>setShowTest(true),\n                    className: \"bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg font-medium\",\n                    children: \"Start Authentic Reading Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 mt-4\",\n                    children: \"This interface is an exact replica of the official IELTS computer-based test system.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\app\\\\demo\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/demo/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/inspera-reading-interface.tsx":
/*!******************************************************!*\
  !*** ./src/components/inspera-reading-interface.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InspecraReadingInterface: () => (/* binding */ InspecraReadingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(ssr)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronLeft,ChevronRight,Menu,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ InspecraReadingInterface auto */ \n\n\n\n\n\n\n\nfunction InspecraReadingInterface({ testTakerId, parts, answers, onAnswerChange, onSubmit }) {\n    const [currentPart, setCurrentPart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentQuestionPage, setCurrentQuestionPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const questionsPerPage = 6;\n    const currentPartData = parts[currentPart];\n    const totalQuestions = currentPartData.questions.length;\n    const totalPages = Math.ceil(totalQuestions / questionsPerPage);\n    const startQuestionIndex = currentQuestionPage * questionsPerPage;\n    const endQuestionIndex = Math.min(startQuestionIndex + questionsPerPage, totalQuestions);\n    const currentQuestions = currentPartData.questions.slice(startQuestionIndex, endQuestionIndex);\n    const renderQuestion = (question)=>{\n        const answer = answers[question.id.toString()];\n        switch(question.type){\n            case 'TRUE_FALSE_NOT_GIVEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                            value: answer || '',\n                            onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"TRUE\",\n                                            id: `${question.id}-true`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-true`,\n                                            className: \"text-sm font-normal\",\n                                            children: \"TRUE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"FALSE\",\n                                            id: `${question.id}-false`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-false`,\n                                            className: \"text-sm font-normal\",\n                                            children: \"FALSE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: \"NOT GIVEN\",\n                                            id: `${question.id}-not-given`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-not-given`,\n                                            className: \"text-sm font-normal\",\n                                            children: \"NOT GIVEN\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this);\n            case 'MULTIPLE_CHOICE':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                            value: answer || '',\n                            onValueChange: (value)=>onAnswerChange(question.id.toString(), value),\n                            className: \"space-y-2\",\n                            children: question.options?.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_3__.RadioGroupItem, {\n                                            value: option,\n                                            id: `${question.id}-${index}`,\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: `${question.id}-${index}`,\n                                            className: \"text-sm font-normal\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this);\n            case 'FILL_IN_BLANK':\n            case 'SHORT_ANSWER':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed\",\n                            children: question.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            value: answer || '',\n                            onChange: (e)=>onAnswerChange(question.id.toString(), e.target.value),\n                            className: \"max-w-xs h-8 text-sm\",\n                            placeholder: \"Type your answer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-600 font-bold text-xl\",\n                                children: \"IELTS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Test taker ID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: [\n                            \"Part \",\n                            currentPartData.partNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-700 mt-1\",\n                        children: currentPartData.instructions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2 border-r border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-4\",\n                                    children: currentPartData.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                    className: \"h-[calc(100vh-280px)]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-sm max-w-none\",\n                                        children: currentPartData.passage.split('\\n\\n').map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4 text-sm leading-relaxed text-gray-800\",\n                                                children: paragraph\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: [\n                                                \"Questions \",\n                                                currentPartData.questionRange\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mt-2\",\n                                            children: [\n                                                \"Choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"TRUE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" if the statement agrees with the information given in the text, choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"FALSE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 117\n                                                }, this),\n                                                \" if the statement contradicts the information, or choose \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"NOT GIVEN\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 70\n                                                }, this),\n                                                \" if there is no information on this.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                    className: \"h-[calc(100vh-320px)]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: currentQuestions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 pb-4 last:border-b-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-gray-900 mt-1 min-w-[20px]\",\n                                                            children: question.id\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: renderQuestion(question)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, question.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: parts.map((part, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setCurrentPart(index);\n                                        setCurrentQuestionPage(0);\n                                    },\n                                    className: `px-3 py-1 text-sm rounded ${currentPart === index ? 'bg-blue-100 text-blue-700 font-medium' : 'text-gray-600 hover:bg-gray-100'}`,\n                                    children: [\n                                        \"Part \",\n                                        part.partNumber\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                    children: Array.from({\n                                        length: totalPages\n                                    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentQuestionPage(i),\n                                            className: `w-6 h-6 text-xs rounded ${currentQuestionPage === i ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                                            children: i + 1\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"Part \",\n                                        currentPartData.partNumber,\n                                        \" \\xa0\\xa0 \",\n                                        startQuestionIndex + 1,\n                                        \" of \",\n                                        totalQuestions\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Part 3 \\xa0\\xa0 0 of 14\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (currentQuestionPage > 0) {\n                                                    setCurrentQuestionPage(currentQuestionPage - 1);\n                                                } else if (currentPart > 0) {\n                                                    setCurrentPart(currentPart - 1);\n                                                    setCurrentQuestionPage(Math.ceil(parts[currentPart - 1].questions.length / questionsPerPage) - 1);\n                                                }\n                                            },\n                                            disabled: currentPart === 0 && currentQuestionPage === 0,\n                                            className: \"w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (currentQuestionPage < totalPages - 1) {\n                                                    setCurrentQuestionPage(currentQuestionPage + 1);\n                                                } else if (currentPart < parts.length - 1) {\n                                                    setCurrentPart(currentPart + 1);\n                                                    setCurrentQuestionPage(0);\n                                                } else {\n                                                    onSubmit();\n                                                }\n                                            },\n                                            className: \"w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronLeft_ChevronRight_Menu_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\inspera-reading-interface.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/inspera-reading-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU0xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSUVMVFMgcHJhY3RpY2VcXGllbHRzLXByYWN0aWNlXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\nfunction Label({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFFdkI7QUFFaEMsU0FBU0csTUFBTSxFQUNiQyxTQUFTLEVBQ1QsR0FBR0MsT0FDOEM7SUFDakQscUJBQ0UsOERBQUNKLHVEQUFtQjtRQUNsQk0sYUFBVTtRQUNWSCxXQUFXRiw4Q0FBRUEsQ0FDWCx1TkFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBMYWJlbCh7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJsYWJlbFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gbGVhZGluZy1ub25lIGZvbnQtbWVkaXVtIHNlbGVjdC1ub25lIGdyb3VwLWRhdGEtW2Rpc2FibGVkPXRydWVdOnBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtZGF0YS1bZGlzYWJsZWQ9dHJ1ZV06b3BhY2l0eS01MCBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjbiIsIkxhYmVsIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/radio-group.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/radio-group.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioGroupItem: () => (/* binding */ RadioGroupItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-radio-group */ \"(ssr)/./node_modules/@radix-ui/react-radio-group/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RadioGroup,RadioGroupItem auto */ \n\n\n\n\nfunction RadioGroup({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"radio-group\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"grid gap-3\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RadioGroupItem({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"radio-group-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_radio_group__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            \"data-slot\": \"radio-group-indicator\",\n            className: \"relative flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\radio-group.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/radio-group.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nfunction ScrollArea({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"scroll-area\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                \"data-slot\": \"scroll-area-viewport\",\n                className: \"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction ScrollBar({ className, orientation = \"vertical\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        \"data-slot\": \"scroll-area-scrollbar\",\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none p-px transition-colors select-none\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            \"data-slot\": \"scroll-area-thumb\",\n            className: \"bg-border relative flex-1 rounded-full\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\IELTS practice\\\\ielts-practice\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/authentic-reading-test.ts":
/*!********************************************!*\
  !*** ./src/data/authentic-reading-test.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticReadingTest: () => (/* binding */ authenticReadingTest)\n/* harmony export */ });\nconst authenticReadingTest = {\n    testId: \"ielts-reading-authentic-1\",\n    title: \"IELTS Academic Reading Test\",\n    duration: 60,\n    parts: [\n        {\n            partNumber: 1,\n            title: \"The life and work of Marie Curie\",\n            instructions: \"Read the text and answer questions 1–13.\",\n            questionRange: \"1–6\",\n            passage: `Marie Curie is probably the most famous woman scientist who has ever lived. Born Maria Sklodowska in Poland in 1867, she is famous for her work on radioactivity, and was twice a winner of the Nobel Prize. With her husband, Pierre Curie, and Henri Becquerel, she was awarded the 1903 Nobel Prize for Physics, and was then sole winner of the 1911 Nobel Prize for Chemistry. She was the first woman to win a Nobel Prize.\n\nFrom childhood, Marie was remarkable for her prodigious memory, and at the age of 16 won a gold medal on completion of her secondary education. Because her father lost his savings through bad investment, she then had to take work as a teacher. From her earnings she was able to finance her sister Bronya's medical studies in Paris, on the understanding that Bronya would, in turn, later help her to get an education.\n\nIn 1891 this promise was fulfilled and Marie went to Paris and began to study at the Sorbonne (the University of Paris). She often worked far into the night and lived on little more than bread and butter and tea. She came first in the examination in the physical sciences in 1893, and in 1894 was placed second in the examination in mathematical sciences. It was not until the spring of that year that she was introduced to Pierre Curie.\n\nTheir marriage in 1895 marked the start of a partnership that was soon to achieve results of world significance. Following Henri Becquerel's discovery in 1896 of the phenomenon that came to be known as radioactivity, Marie Curie decided to investigate whether any other elements gave off this mysterious radiation. She discovered that thorium gave off the same radiation as uranium. Her continued systematic investigations led to the discovery of the new elements radium and polonium, named after her native land. She and her husband were awarded the Nobel Prize for Physics in 1903.\n\nPierre's death in 1906 was a bitter blow to Marie, but it also marked the beginning of her most productive period. She was appointed to her husband's chair at the Sorbonne, thus becoming the first woman professor in the university's 650-year history. The radium institute, which she had been instrumental in founding, was completed in 1914, and it was here that she spent the last 20 years of her life.`,\n            questions: [\n                {\n                    id: 1,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie Curie's husband was a joint winner of both Marie's Nobel Prizes.\"\n                },\n                {\n                    id: 2,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie became interested in science when she was a child.\"\n                },\n                {\n                    id: 3,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie was able to attend the Sorbonne because of her sister's financial contribution.\"\n                },\n                {\n                    id: 4,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Marie stopped doing research for several years when Pierre died.\"\n                },\n                {\n                    id: 5,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The Radium Institute was built after Marie's death.\"\n                },\n                {\n                    id: 6,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The Radium Institute was the first research facility of its kind.\"\n                }\n            ]\n        },\n        {\n            partNumber: 2,\n            title: \"The Development of Plastic Surgery\",\n            instructions: \"Read the text and answer questions 14–26.\",\n            questionRange: \"14–20\",\n            passage: `Plastic surgery is a medical specialty concerned with the correction or restoration of form and function. While famous for aesthetic surgery, plastic surgery also includes many types of reconstructive surgery, hand surgery, microsurgery, and the treatment of burns.\n\nThe word \"plastic\" derives from the Greek plastikos meaning \"to mold\" or \"to shape\"; it is not related to the synthetic polymer material known as plastic. The art and science of plastic surgery has ancient origins. As early as 800 BC, surgeons in India were using skin grafts for reconstructive work, and by 600 BC, they were performing cosmetic surgery. Ancient Romans were also able to perform simple techniques, such as repairing damaged ears.\n\nBecause of the social taboo surrounding surgery in the Middle Ages, advances in plastic surgery did not come until the Renaissance period in Europe. Heinrich von Pfolspeundt described a process \"to make a new nose for one who lacks it entirely\" by removing skin from the back of the arm and attaching it to the nose area. By the 15th and 16th centuries, European surgeons were able to carry out skin grafts and other forms of reconstructive surgery.\n\nModern plastic surgery developed out of the need to treat facial injuries resulting from World War I. Many servicemen suffered severe facial wounds from shrapnel, and traditional medicine was inadequate to treat such injuries. Harold Gillies, a New Zealand-born surgeon working for the British army, is considered the father of modern plastic surgery. He developed many techniques that are still used today and established the first hospital unit dedicated entirely to plastic surgery.\n\nThe development of new techniques continued throughout the 20th century. The introduction of antibiotics greatly reduced the risk of infection, while new anesthetic techniques made longer, more complex operations possible. Today, plastic surgery encompasses a wide range of procedures, from reconstructive work following accidents or cancer treatment to cosmetic procedures designed to enhance appearance.`,\n            questions: [\n                {\n                    id: 14,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The word 'plastic' in plastic surgery refers to:\",\n                    options: [\n                        \"the use of synthetic materials\",\n                        \"the ability to mold or shape\",\n                        \"a type of medical instrument\",\n                        \"the flexibility of human tissue\"\n                    ]\n                },\n                {\n                    id: 15,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Cosmetic surgery was performed in India before 600 BC.\"\n                },\n                {\n                    id: 16,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Ancient Romans could perform more complex surgery than Indians.\"\n                },\n                {\n                    id: 17,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Heinrich von Pfolspeundt described how to create a new _______ using skin from the arm.\"\n                },\n                {\n                    id: 18,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Harold Gillies was born in Britain.\"\n                },\n                {\n                    id: 19,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"Modern plastic surgery developed primarily because of:\",\n                    options: [\n                        \"advances in anesthetic techniques\",\n                        \"the need to treat war injuries\",\n                        \"the invention of antibiotics\",\n                        \"increased demand for cosmetic procedures\"\n                    ]\n                },\n                {\n                    id: 20,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Antibiotics made plastic surgery completely safe.\"\n                }\n            ]\n        },\n        {\n            partNumber: 3,\n            title: \"The Impact of Social Media on Modern Communication\",\n            instructions: \"Read the text and answer questions 27–40.\",\n            questionRange: \"27–33\",\n            passage: `Social media has fundamentally transformed the way humans communicate, creating unprecedented opportunities for connection while simultaneously raising concerns about the quality and authenticity of modern interactions. The rise of platforms such as Facebook, Twitter, Instagram, and TikTok has created a global communication network that operates 24 hours a day, seven days a week.\n\nOne of the most significant impacts of social media has been the democratization of information sharing. Previously, the dissemination of news and information was controlled by traditional media outlets such as newspapers, television, and radio stations. Today, any individual with internet access can share information instantly with a global audience. This has led to both positive and negative consequences. On the positive side, social movements have been able to organize more effectively, marginalized voices have found platforms to express themselves, and breaking news can be shared in real-time. However, this democratization has also led to the spread of misinformation and the creation of echo chambers where people are exposed only to information that confirms their existing beliefs.\n\nThe psychological impact of social media use has become a subject of intense research and debate. Studies have shown that excessive use of social media can lead to increased feelings of anxiety, depression, and social isolation, particularly among young people. The constant comparison with others' curated online personas can create unrealistic expectations and feelings of inadequacy. Furthermore, the addictive nature of social media platforms, designed to maximize user engagement through intermittent reinforcement schedules, has raised concerns about digital wellness and the need for better regulation of these technologies.\n\nDespite these concerns, social media has also created new opportunities for education, business, and creative expression. Online learning platforms have made education more accessible, small businesses can reach global markets through social media marketing, and artists and creators can build audiences without traditional gatekeepers. The COVID-19 pandemic highlighted the importance of digital communication tools in maintaining social connections during periods of physical isolation.\n\nLooking forward, the challenge will be to harness the benefits of social media while mitigating its negative effects. This will likely require a combination of technological solutions, regulatory frameworks, and digital literacy education to help users navigate the complex landscape of modern digital communication.`,\n            questions: [\n                {\n                    id: 27,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"According to the passage, social media has democratized information sharing by:\",\n                    options: [\n                        \"replacing traditional media outlets entirely\",\n                        \"allowing anyone with internet access to share information globally\",\n                        \"improving the quality of news reporting\",\n                        \"reducing the cost of information distribution\"\n                    ]\n                },\n                {\n                    id: 28,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Echo chambers are created when people only see information that supports their existing beliefs.\"\n                },\n                {\n                    id: 29,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"Young people are more affected by social media's psychological impact than adults.\"\n                },\n                {\n                    id: 30,\n                    type: 'FILL_IN_BLANK',\n                    text: \"Social media platforms use _______ reinforcement schedules to maximize user engagement.\"\n                },\n                {\n                    id: 31,\n                    type: 'TRUE_FALSE_NOT_GIVEN',\n                    text: \"The COVID-19 pandemic proved that digital communication is superior to face-to-face interaction.\"\n                },\n                {\n                    id: 32,\n                    type: 'MULTIPLE_CHOICE',\n                    text: \"The passage suggests that addressing social media's negative effects will require:\",\n                    options: [\n                        \"banning social media platforms entirely\",\n                        \"only technological solutions\",\n                        \"a combination of technology, regulation, and education\",\n                        \"returning to traditional media only\"\n                    ]\n                },\n                {\n                    id: 33,\n                    type: 'SHORT_ANSWER',\n                    text: \"Name two positive outcomes of social media's democratization of information sharing mentioned in the passage.\"\n                }\n            ]\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/authentic-reading-test.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXElFTFRTIHByYWN0aWNlXFxpZWx0cy1wcmFjdGljZVxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CIELTS%20practice%5Cielts-practice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();