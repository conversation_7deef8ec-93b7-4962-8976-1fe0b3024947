{"version": 3, "sources": ["../src/index.ts", "../src/number.ts"], "sourcesContent": ["export { clamp } from './number';\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,SAAS,MAAM,OAAe,CAAC,KAAK,GAAG,GAA6B;AAClE,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;", "names": []}