{"name": "react-style-singleton", "version": "2.2.3", "description": "Just create a single stylesheet...", "main": "dist/es5/index.js", "types": "dist/es5/index.d.ts", "jsnext:main": "dist/es2015/index.js", "module": "dist/es2015/index.js", "module:es2019": "dist/es2019/index.js", "files": ["dist"], "scripts": {"dev": "lib-builder dev", "test": "jest", "test:ci": "jest --runInBand --coverage", "build": "lib-builder build && yarn size:report", "release": "yarn build && yarn test", "size": "npx size-limit", "size:report": "npx size-limit --json > .size.json", "lint": "lib-builder lint", "format": "lib-builder format", "update": "lib-builder update", "prepublish-only": "yarn build && yarn changelog", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:rewrite": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0"}, "repository": {"type": "git", "url": "https://github.com/theKashey/react-style-singleton"}, "bugs": {"url": "https://github.com/theKashey/react-style-singleton/issues"}, "homepage": "https://github.com/theKashey/react-style-singleton#readme", "author": "<PERSON> (<EMAIL>)", "license": "MIT", "devDependencies": {"@theuiteam/lib-builder": "^0.1.4", "@size-limit/preset-small-lib": "^11.0.2", "size-limit": "^11.0.2", "react": "^16.8.6", "react-dom": "^16.8.6"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "keywords": ["react", "style", "css"], "dependencies": {"tslib": "^2.0.0", "get-nonce": "^1.0.0"}, "engines": {"node": ">=10"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix", "git add"], "*.{js,css,json,md}": ["prettier --write", "git add"]}, "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true}, "packageManager": "yarn@1.22.19"}