{"version": 3, "sources": ["../src/menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ComponentRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst Slot = createSlot('MenuContent.ScrollLock');\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ComponentRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ComponentRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,iBAAiB,mBAAmB;AAC7C,SAAS,0BAA0B;AACnC,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AACjC,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,aAAa;AACtB,YAAY,qBAAqB;AACjC,SAAS,yBAAyB;AAClC,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,WAAW,mCAAmC;AACvD,YAAY,sBAAsB;AAClC,SAAS,mCAAmC;AAC5C,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAoGrB;AA9FR,IAAM,iBAAiB,CAAC,SAAS,GAAG;AACpC,IAAM,aAAa,CAAC,aAAa,UAAU,MAAM;AACjD,IAAM,YAAY,CAAC,WAAW,YAAY,KAAK;AAC/C,IAAM,kBAAkB,CAAC,GAAG,YAAY,GAAG,SAAS;AACpD,IAAM,gBAA6C;AAAA,EACjD,KAAK,CAAC,GAAG,gBAAgB,YAAY;AAAA,EACrC,KAAK,CAAC,GAAG,gBAAgB,WAAW;AACtC;AACA,IAAM,iBAA8C;AAAA,EAClD,KAAK,CAAC,WAAW;AAAA,EACjB,KAAK,CAAC,YAAY;AACpB;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAGzD,SAAS;AAGX,IAAM,CAAC,mBAAmB,eAAe,IAAI,mBAAmB,WAAW;AAAA,EACzE;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AACzC,IAAM,2BAA2B,4BAA4B;AAS7D,IAAM,CAAC,cAAc,cAAc,IAAI,kBAAoC,SAAS;AASpF,IAAM,CAAC,kBAAkB,kBAAkB,IAAI,kBAAwC,SAAS;AAUhG,IAAM,OAA4B,CAAC,UAAkC;AACnE,QAAM,EAAE,aAAa,OAAO,OAAO,UAAU,KAAK,cAAc,QAAQ,KAAK,IAAI;AACjF,QAAM,cAAc,eAAe,WAAW;AAC9C,QAAM,CAAC,SAAS,UAAU,IAAU,eAAoC,IAAI;AAC5E,QAAM,qBAA2B,aAAO,KAAK;AAC7C,QAAM,mBAAmB,eAAe,YAAY;AACpD,QAAM,YAAY,aAAa,GAAG;AAElC,EAAM,gBAAU,MAAM;AAGpB,UAAM,gBAAgB,MAAM;AAC1B,yBAAmB,UAAU;AAC7B,eAAS,iBAAiB,eAAe,eAAe,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;AACrF,eAAS,iBAAiB,eAAe,eAAe,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;AAAA,IACvF;AACA,UAAM,gBAAgB,MAAO,mBAAmB,UAAU;AAC1D,aAAS,iBAAiB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;AACrE,WAAO,MAAM;AACX,eAAS,oBAAoB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;AACxE,eAAS,oBAAoB,eAAe,eAAe,EAAE,SAAS,KAAK,CAAC;AAC5E,eAAS,oBAAoB,eAAe,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,IAC9E;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SACE,oBAAiB,sBAAhB,EAAsB,GAAG,aACxB;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,MAEjB;AAAA,QAAC;AAAA;AAAA,UACC,OAAO;AAAA,UACP,SAAe,kBAAY,MAAM,iBAAiB,KAAK,GAAG,CAAC,gBAAgB,CAAC;AAAA,UAC5E;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UAEC;AAAA;AAAA,MACH;AAAA;AAAA,EACF,GACF;AAEJ;AAEA,KAAK,cAAc;AAMnB,IAAM,cAAc;AAMpB,IAAM,aAAmB;AAAA,EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,aAAa,GAAG,YAAY,IAAI;AACxC,UAAM,cAAc,eAAe,WAAW;AAC9C,WAAO,oBAAiB,wBAAhB,EAAwB,GAAG,aAAc,GAAG,aAAa,KAAK,cAAc;AAAA,EACtF;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,kBAAsC,aAAa;AAAA,EAC5F,YAAY;AACd,CAAC;AAgBD,IAAM,aAAwC,CAAC,UAAwC;AACrF,QAAM,EAAE,aAAa,YAAY,UAAU,UAAU,IAAI;AACzD,QAAM,UAAU,eAAe,aAAa,WAAW;AACvD,SACE,oBAAC,kBAAe,OAAO,aAAa,YAClC,8BAAC,YAAS,SAAS,cAAc,QAAQ,MACvC,8BAAC,mBAAgB,SAAO,MAAC,WACtB,UACH,GACF,GACF;AAEJ;AAEA,WAAW,cAAc;AAMzB,IAAM,eAAe;AAUrB,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,kBAA2C,YAAY;AAgBzD,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;AACtE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,UAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;AAEtE,WACE,oBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,aAChC,8BAAC,YAAS,SAAS,cAAc,QAAQ,MACvC,8BAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,aAC3B,sBAAY,QACX,oBAAC,wBAAsB,GAAG,cAAc,KAAK,cAAc,IAE3D,oBAAC,2BAAyB,GAAG,cAAc,KAAK,cAAc,GAElE,GACF,GACF;AAAA,EAEJ;AACF;AAQA,IAAM,uBAA6B;AAAA,EACjC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,UAAM,MAAY,aAAmC,IAAI;AACzD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AAGtD,IAAM,gBAAU,MAAM;AACpB,YAAM,UAAU,IAAI;AACpB,UAAI,QAAS,QAAO,WAAW,OAAO;AAAA,IACxC,GAAG,CAAC,CAAC;AAEL,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QAGL,WAAW,QAAQ;AAAA,QAGnB,6BAA6B,QAAQ;AAAA,QACrC,sBAAoB;AAAA,QAGpB,gBAAgB;AAAA,UACd,MAAM;AAAA,UACN,CAAC,UAAU,MAAM,eAAe;AAAA,UAChC,EAAE,0BAA0B,MAAM;AAAA,QACpC;AAAA,QACA,WAAW,MAAM,QAAQ,aAAa,KAAK;AAAA;AAAA,IAC7C;AAAA,EAEJ;AACF;AAEA,IAAM,0BAAgC,iBAGpC,CAAC,OAA8C,iBAAiB;AAChE,QAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,SACE;AAAA,IAAC;AAAA;AAAA,MACE,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,WAAW;AAAA,MACX,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,WAAW,MAAM,QAAQ,aAAa,KAAK;AAAA;AAAA,EAC7C;AAEJ,CAAC;AAgDD,IAAM,OAAO,WAAW,wBAAwB;AAEhD,IAAM,kBAAwB;AAAA,EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,cAAc,mBAAmB,cAAc,WAAW;AAChE,UAAM,cAAc,eAAe,WAAW;AAC9C,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,WAAW,cAAc,WAAW;AAC1C,UAAM,CAAC,eAAe,gBAAgB,IAAU,eAAwB,IAAI;AAC5E,UAAM,aAAmB,aAAuB,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,YAAY,QAAQ,eAAe;AACtF,UAAM,WAAiB,aAAO,CAAC;AAC/B,UAAM,YAAkB,aAAO,EAAE;AACjC,UAAM,uBAA6B,aAAO,CAAC;AAC3C,UAAM,wBAA8B,aAA2B,IAAI;AACnE,UAAM,gBAAsB,aAAa,OAAO;AAChD,UAAM,kBAAwB,aAAO,CAAC;AAEtC,UAAM,oBAAoB,uBAAuB,eAAqB;AACtE,UAAM,yBAAyB,uBAC3B,EAAE,IAAI,MAAM,gBAAgB,KAAK,IACjC;AAEJ,UAAM,wBAAwB,CAAC,QAAgB;AAC7C,YAAM,SAAS,UAAU,UAAU;AACnC,YAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,YAAM,cAAc,SAAS;AAC7B,YAAM,eAAe,MAAM,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,WAAW,GAAG;AAC7E,YAAM,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,SAAS;AACjD,YAAM,YAAY,aAAa,QAAQ,QAAQ,YAAY;AAC3D,YAAM,UAAU,MAAM,KAAK,CAAC,SAAS,KAAK,cAAc,SAAS,GAAG,IAAI;AAGxE,OAAC,SAAS,aAAa,OAAe;AACpC,kBAAU,UAAU;AACpB,eAAO,aAAa,SAAS,OAAO;AACpC,YAAI,UAAU,GAAI,UAAS,UAAU,OAAO,WAAW,MAAM,aAAa,EAAE,GAAG,GAAI;AAAA,MACrF,GAAG,MAAM;AAET,UAAI,SAAS;AAKX,mBAAW,MAAO,QAAwB,MAAM,CAAC;AAAA,MACnD;AAAA,IACF;AAEA,IAAM,gBAAU,MAAM;AACpB,aAAO,MAAM,OAAO,aAAa,SAAS,OAAO;AAAA,IACnD,GAAG,CAAC,CAAC;AAIL,mBAAe;AAEf,UAAM,2BAAiC,kBAAY,CAAC,UAA8B;AAChF,YAAM,kBAAkB,cAAc,YAAY,sBAAsB,SAAS;AACjF,aAAO,mBAAmB,qBAAqB,OAAO,sBAAsB,SAAS,IAAI;AAAA,IAC3F,GAAG,CAAC,CAAC;AAEL,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,aAAmB;AAAA,UACjB,CAAC,UAAU;AACT,gBAAI,yBAAyB,KAAK,EAAG,OAAM,eAAe;AAAA,UAC5D;AAAA,UACA,CAAC,wBAAwB;AAAA,QAC3B;AAAA,QACA,aAAmB;AAAA,UACjB,CAAC,UAAU;AACT,gBAAI,yBAAyB,KAAK,EAAG;AACrC,uBAAW,SAAS,MAAM;AAC1B,6BAAiB,IAAI;AAAA,UACvB;AAAA,UACA,CAAC,wBAAwB;AAAA,QAC3B;AAAA,QACA,gBAAsB;AAAA,UACpB,CAAC,UAAU;AACT,gBAAI,yBAAyB,KAAK,EAAG,OAAM,eAAe;AAAA,UAC5D;AAAA,UACA,CAAC,wBAAwB;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,4BAAkC,kBAAY,CAAC,WAAW;AACxD,gCAAsB,UAAU;AAAA,QAClC,GAAG,CAAC,CAAC;AAAA,QAEL,8BAAC,qBAAmB,GAAG,wBACrB;AAAA,UAAC;AAAA;AAAA,YACC,SAAO;AAAA,YACP,SAAS;AAAA,YACT,kBAAkB,qBAAqB,iBAAiB,CAAC,UAAU;AAGjE,oBAAM,eAAe;AACrB,yBAAW,SAAS,MAAM,EAAE,eAAe,KAAK,CAAC;AAAA,YACnD,CAAC;AAAA,YACD,oBAAoB;AAAA,YAEpB;AAAA,cAAC;AAAA;AAAA,gBACC,SAAO;AAAA,gBACP;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBAEA;AAAA,kBAAkB;AAAA,kBAAjB;AAAA,oBACC,SAAO;AAAA,oBACN,GAAG;AAAA,oBACJ,KAAK,YAAY;AAAA,oBACjB,aAAY;AAAA,oBACZ;AAAA,oBACA,kBAAkB;AAAA,oBAClB,0BAA0B;AAAA,oBAC1B,cAAc,qBAAqB,cAAc,CAAC,UAAU;AAE1D,0BAAI,CAAC,YAAY,mBAAmB,QAAS,OAAM,eAAe;AAAA,oBACpE,CAAC;AAAA,oBACD,2BAAyB;AAAA,oBAEzB;AAAA,sBAAiB;AAAA,sBAAhB;AAAA,wBACC,MAAK;AAAA,wBACL,oBAAiB;AAAA,wBACjB,cAAY,aAAa,QAAQ,IAAI;AAAA,wBACrC,2BAAwB;AAAA,wBACxB,KAAK,YAAY;AAAA,wBAChB,GAAG;AAAA,wBACH,GAAG;AAAA,wBACJ,KAAK;AAAA,wBACL,OAAO,EAAE,SAAS,QAAQ,GAAG,aAAa,MAAM;AAAA,wBAChD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AAEjE,gCAAM,SAAS,MAAM;AACrB,gCAAM,kBACJ,OAAO,QAAQ,2BAA2B,MAAM,MAAM;AACxD,gCAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,gCAAM,iBAAiB,MAAM,IAAI,WAAW;AAC5C,8BAAI,iBAAiB;AAEnB,gCAAI,MAAM,QAAQ,MAAO,OAAM,eAAe;AAC9C,gCAAI,CAAC,iBAAiB,eAAgB,uBAAsB,MAAM,GAAG;AAAA,0BACvE;AAEA,gCAAM,UAAU,WAAW;AAC3B,8BAAI,MAAM,WAAW,QAAS;AAC9B,8BAAI,CAAC,gBAAgB,SAAS,MAAM,GAAG,EAAG;AAC1C,gCAAM,eAAe;AACrB,gCAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,gCAAM,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAC5D,8BAAI,UAAU,SAAS,MAAM,GAAG,EAAG,gBAAe,QAAQ;AAC1D,qCAAW,cAAc;AAAA,wBAC3B,CAAC;AAAA,wBACD,QAAQ,qBAAqB,MAAM,QAAQ,CAAC,UAAU;AAEpD,8BAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,GAAG;AAC/C,mCAAO,aAAa,SAAS,OAAO;AACpC,sCAAU,UAAU;AAAA,0BACtB;AAAA,wBACF,CAAC;AAAA,wBACD,eAAe;AAAA,0BACb,MAAM;AAAA,0BACN,UAAU,CAAC,UAAU;AACnB,kCAAM,SAAS,MAAM;AACrB,kCAAM,qBAAqB,gBAAgB,YAAY,MAAM;AAI7D,gCAAI,MAAM,cAAc,SAAS,MAAM,KAAK,oBAAoB;AAC9D,oCAAM,SAAS,MAAM,UAAU,gBAAgB,UAAU,UAAU;AACnE,4CAAc,UAAU;AACxB,8CAAgB,UAAU,MAAM;AAAA,4BAClC;AAAA,0BACF,CAAC;AAAA,wBACH;AAAA;AAAA,oBACF;AAAA;AAAA,gBACF;AAAA;AAAA,YACF;AAAA;AAAA,QACF,GACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,YAAkB;AAAA,EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,WAAO,oBAAC,UAAU,KAAV,EAAc,MAAK,SAAS,GAAG,YAAY,KAAK,cAAc;AAAA,EACxE;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,aAAa;AAKnB,IAAM,YAAkB;AAAA,EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,WAAO,oBAAC,UAAU,KAAV,EAAe,GAAG,YAAY,KAAK,cAAc;AAAA,EAC3D;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,YAAY;AAClB,IAAM,cAAc;AAOpB,IAAM,WAAiB;AAAA,EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM,EAAE,WAAW,OAAO,UAAU,GAAG,UAAU,IAAI;AACrD,UAAM,MAAY,aAAuB,IAAI;AAC7C,UAAM,cAAc,mBAAmB,WAAW,MAAM,WAAW;AACnE,UAAM,iBAAiB,sBAAsB,WAAW,MAAM,WAAW;AACzE,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,mBAAyB,aAAO,KAAK;AAE3C,UAAM,eAAe,MAAM;AACzB,YAAM,WAAW,IAAI;AACrB,UAAI,CAAC,YAAY,UAAU;AACzB,cAAM,kBAAkB,IAAI,YAAY,aAAa,EAAE,SAAS,MAAM,YAAY,KAAK,CAAC;AACxF,iBAAS,iBAAiB,aAAa,CAAC,UAAU,WAAW,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC;AACnF,oCAA4B,UAAU,eAAe;AACrD,YAAI,gBAAgB,kBAAkB;AACpC,2BAAiB,UAAU;AAAA,QAC7B,OAAO;AACL,sBAAY,QAAQ;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QACL;AAAA,QACA,SAAS,qBAAqB,MAAM,SAAS,YAAY;AAAA,QACzD,eAAe,CAAC,UAAU;AACxB,gBAAM,gBAAgB,KAAK;AAC3B,2BAAiB,UAAU;AAAA,QAC7B;AAAA,QACA,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAI9D,cAAI,CAAC,iBAAiB,QAAS,OAAM,eAAe,MAAM;AAAA,QAC5D,CAAC;AAAA,QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,gBAAgB,eAAe,UAAU,YAAY;AAC3D,cAAI,YAAa,iBAAiB,MAAM,QAAQ,IAAM;AACtD,cAAI,eAAe,SAAS,MAAM,GAAG,GAAG;AACtC,kBAAM,cAAc,MAAM;AAO1B,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA;AAAA,IACH;AAAA,EAEJ;AACF;AAEA,SAAS,cAAc;AAUvB,IAAM,eAAqB;AAAA,EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,aAAa,WAAW,OAAO,WAAW,GAAG,UAAU,IAAI;AACnE,UAAM,iBAAiB,sBAAsB,WAAW,WAAW;AACnE,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,MAAY,aAAuB,IAAI;AAC7C,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,CAAC,WAAW,YAAY,IAAU,eAAS,KAAK;AAGtD,UAAM,CAAC,aAAa,cAAc,IAAU,eAAS,EAAE;AACvD,IAAM,gBAAU,MAAM;AACpB,YAAM,WAAW,IAAI;AACrB,UAAI,UAAU;AACZ,wBAAgB,SAAS,eAAe,IAAI,KAAK,CAAC;AAAA,MACpD;AAAA,IACF,GAAG,CAAC,UAAU,QAAQ,CAAC;AAEvB,WACE;AAAA,MAAC,WAAW;AAAA,MAAX;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,WAAW,aAAa;AAAA,QAExB,8BAAkB,uBAAjB,EAAsB,SAAO,MAAE,GAAG,uBAAuB,WAAW,CAAC,UACpE;AAAA,UAAC,UAAU;AAAA,UAAV;AAAA,YACC,MAAK;AAAA,YACL,oBAAkB,YAAY,KAAK;AAAA,YACnC,iBAAe,YAAY;AAAA,YAC3B,iBAAe,WAAW,KAAK;AAAA,YAC9B,GAAG;AAAA,YACJ,KAAK;AAAA,YAYL,eAAe;AAAA,cACb,MAAM;AAAA,cACN,UAAU,CAAC,UAAU;AACnB,oBAAI,UAAU;AACZ,iCAAe,YAAY,KAAK;AAAA,gBAClC,OAAO;AACL,iCAAe,YAAY,KAAK;AAChC,sBAAI,CAAC,MAAM,kBAAkB;AAC3B,0BAAM,OAAO,MAAM;AACnB,yBAAK,MAAM,EAAE,eAAe,KAAK,CAAC;AAAA,kBACpC;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,gBAAgB;AAAA,cACd,MAAM;AAAA,cACN,UAAU,CAAC,UAAU,eAAe,YAAY,KAAK,CAAC;AAAA,YACxD;AAAA,YACA,SAAS,qBAAqB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;AAAA,YACrE,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,aAAa,KAAK,CAAC;AAAA;AAAA,QACtE,GACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAMA,IAAM,qBAAqB;AAY3B,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,UAAU,OAAO,iBAAiB,GAAG,kBAAkB,IAAI;AACnE,WACE,oBAAC,yBAAsB,OAAO,MAAM,aAAa,SAC/C;AAAA,MAAC;AAAA;AAAA,QACC,MAAK;AAAA,QACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;AAAA,QAClD,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,cAAY,gBAAgB,OAAO;AAAA,QACnC,UAAU;AAAA,UACR,kBAAkB;AAAA,UAClB,MAAM,kBAAkB,gBAAgB,OAAO,IAAI,OAAO,CAAC,OAAO;AAAA,UAClE,EAAE,0BAA0B,MAAM;AAAA,QACpC;AAAA;AAAA,IACF,GACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,mBAAmB;AAEzB,IAAM,CAAC,oBAAoB,oBAAoB,IAAI;AAAA,EACjD;AAAA,EACA,EAAE,OAAO,QAAW,eAAe,MAAM;AAAA,EAAC,EAAE;AAC9C;AAQA,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,OAAO,eAAe,GAAG,WAAW,IAAI;AAChD,UAAM,oBAAoB,eAAe,aAAa;AACtD,WACE,oBAAC,sBAAmB,OAAO,MAAM,aAAa,OAAc,eAAe,mBACzE,8BAAC,aAAW,GAAG,YAAY,KAAK,cAAc,GAChD;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,kBAAkB;AAOxB,IAAM,gBAAsB;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,OAAO,GAAG,eAAe,IAAI;AACrC,UAAM,UAAU,qBAAqB,iBAAiB,MAAM,WAAW;AACvE,UAAM,UAAU,UAAU,QAAQ;AAClC,WACE,oBAAC,yBAAsB,OAAO,MAAM,aAAa,SAC/C;AAAA,MAAC;AAAA;AAAA,QACC,MAAK;AAAA,QACL,gBAAc;AAAA,QACb,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,cAAY,gBAAgB,OAAO;AAAA,QACnC,UAAU;AAAA,UACR,eAAe;AAAA,UACf,MAAM,QAAQ,gBAAgB,KAAK;AAAA,UACnC,EAAE,0BAA0B,MAAM;AAAA,QACpC;AAAA;AAAA,IACF,GACF;AAAA,EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,sBAAsB;AAI5B,IAAM,CAAC,uBAAuB,uBAAuB,IAAI;AAAA,EACvD;AAAA,EACA,EAAE,SAAS,MAAM;AACnB;AAYA,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,aAAa,YAAY,GAAG,mBAAmB,IAAI;AAC3D,UAAM,mBAAmB,wBAAwB,qBAAqB,WAAW;AACjF,WACE;AAAA,MAAC;AAAA;AAAA,QACC,SACE,cACA,gBAAgB,iBAAiB,OAAO,KACxC,iBAAiB,YAAY;AAAA,QAG/B;AAAA,UAAC,UAAU;AAAA,UAAV;AAAA,YACE,GAAG;AAAA,YACJ,KAAK;AAAA,YACL,cAAY,gBAAgB,iBAAiB,OAAO;AAAA;AAAA,QACtD;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,iBAAiB;AAKvB,IAAM,gBAAsB;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,aAAa,GAAG,eAAe,IAAI;AAC3C,WACE;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,MAAK;AAAA,QACL,oBAAiB;AAAA,QAChB,GAAG;AAAA,QACJ,KAAK;AAAA;AAAA,IACP;AAAA,EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAMnB,IAAM,YAAkB;AAAA,EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,UAAM,cAAc,eAAe,WAAW;AAC9C,WAAO,oBAAiB,uBAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,cAAc;AAAA,EACpF;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,WAAW;AASjB,IAAM,CAAC,iBAAiB,iBAAiB,IAAI,kBAAuC,QAAQ;AAQ5F,IAAM,UAAkC,CAAC,UAAqC;AAC5E,QAAM,EAAE,aAAa,UAAU,OAAO,OAAO,aAAa,IAAI;AAC9D,QAAM,oBAAoB,eAAe,UAAU,WAAW;AAC9D,QAAM,cAAc,eAAe,WAAW;AAC9C,QAAM,CAAC,SAAS,UAAU,IAAU,eAAuC,IAAI;AAC/E,QAAM,CAAC,SAAS,UAAU,IAAU,eAAoC,IAAI;AAC5E,QAAM,mBAAmB,eAAe,YAAY;AAGpD,EAAM,gBAAU,MAAM;AACpB,QAAI,kBAAkB,SAAS,MAAO,kBAAiB,KAAK;AAC5D,WAAO,MAAM,iBAAiB,KAAK;AAAA,EACrC,GAAG,CAAC,kBAAkB,MAAM,gBAAgB,CAAC;AAE7C,SACE,oBAAiB,sBAAhB,EAAsB,GAAG,aACxB;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,MAEjB;AAAA,QAAC;AAAA;AAAA,UACC,OAAO;AAAA,UACP,WAAW,MAAM;AAAA,UACjB,WAAW,MAAM;AAAA,UACjB;AAAA,UACA,iBAAiB;AAAA,UAEhB;AAAA;AAAA,MACH;AAAA;AAAA,EACF,GACF;AAEJ;AAEA,QAAQ,cAAc;AAMtB,IAAM,mBAAmB;AAKzB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,UAAU,eAAe,kBAAkB,MAAM,WAAW;AAClE,UAAM,cAAc,mBAAmB,kBAAkB,MAAM,WAAW;AAC1E,UAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;AACxE,UAAM,iBAAiB,sBAAsB,kBAAkB,MAAM,WAAW;AAChF,UAAM,eAAqB,aAAsB,IAAI;AACrD,UAAM,EAAE,sBAAsB,2BAA2B,IAAI;AAC7D,UAAM,QAAQ,EAAE,aAAa,MAAM,YAAY;AAE/C,UAAM,iBAAuB,kBAAY,MAAM;AAC7C,UAAI,aAAa,QAAS,QAAO,aAAa,aAAa,OAAO;AAClE,mBAAa,UAAU;AAAA,IACzB,GAAG,CAAC,CAAC;AAEL,IAAM,gBAAU,MAAM,gBAAgB,CAAC,cAAc,CAAC;AAEtD,IAAM,gBAAU,MAAM;AACpB,YAAM,oBAAoB,qBAAqB;AAC/C,aAAO,MAAM;AACX,eAAO,aAAa,iBAAiB;AACrC,mCAA2B,IAAI;AAAA,MACjC;AAAA,IACF,GAAG,CAAC,sBAAsB,0BAA0B,CAAC;AAErD,WACE,oBAAC,cAAW,SAAO,MAAE,GAAG,OACtB;AAAA,MAAC;AAAA;AAAA,QACC,IAAI,WAAW;AAAA,QACf,iBAAc;AAAA,QACd,iBAAe,QAAQ;AAAA,QACvB,iBAAe,WAAW;AAAA,QAC1B,cAAY,aAAa,QAAQ,IAAI;AAAA,QACpC,GAAG;AAAA,QACJ,KAAK,YAAY,cAAc,WAAW,eAAe;AAAA,QAGzD,SAAS,CAAC,UAAU;AAClB,gBAAM,UAAU,KAAK;AACrB,cAAI,MAAM,YAAY,MAAM,iBAAkB;AAM9C,gBAAM,cAAc,MAAM;AAC1B,cAAI,CAAC,QAAQ,KAAM,SAAQ,aAAa,IAAI;AAAA,QAC9C;AAAA,QACA,eAAe;AAAA,UACb,MAAM;AAAA,UACN,UAAU,CAAC,UAAU;AACnB,2BAAe,YAAY,KAAK;AAChC,gBAAI,MAAM,iBAAkB;AAC5B,gBAAI,CAAC,MAAM,YAAY,CAAC,QAAQ,QAAQ,CAAC,aAAa,SAAS;AAC7D,6BAAe,2BAA2B,IAAI;AAC9C,2BAAa,UAAU,OAAO,WAAW,MAAM;AAC7C,wBAAQ,aAAa,IAAI;AACzB,+BAAe;AAAA,cACjB,GAAG,GAAG;AAAA,YACR;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,gBAAgB;AAAA,UACd,MAAM;AAAA,UACN,UAAU,CAAC,UAAU;AACnB,2BAAe;AAEf,kBAAM,cAAc,QAAQ,SAAS,sBAAsB;AAC3D,gBAAI,aAAa;AAEf,oBAAM,OAAO,QAAQ,SAAS,QAAQ;AACtC,oBAAM,YAAY,SAAS;AAC3B,oBAAM,QAAQ,YAAY,KAAK;AAC/B,oBAAM,kBAAkB,YAAY,YAAY,SAAS,OAAO;AAChE,oBAAM,iBAAiB,YAAY,YAAY,UAAU,MAAM;AAE/D,6BAAe,2BAA2B;AAAA,gBACxC,MAAM;AAAA;AAAA;AAAA,kBAGJ,EAAE,GAAG,MAAM,UAAU,OAAO,GAAG,MAAM,QAAQ;AAAA,kBAC7C,EAAE,GAAG,iBAAiB,GAAG,YAAY,IAAI;AAAA,kBACzC,EAAE,GAAG,gBAAgB,GAAG,YAAY,IAAI;AAAA,kBACxC,EAAE,GAAG,gBAAgB,GAAG,YAAY,OAAO;AAAA,kBAC3C,EAAE,GAAG,iBAAiB,GAAG,YAAY,OAAO;AAAA,gBAC9C;AAAA,gBACA;AAAA,cACF,CAAC;AAED,qBAAO,aAAa,qBAAqB,OAAO;AAChD,mCAAqB,UAAU,OAAO;AAAA,gBACpC,MAAM,eAAe,2BAA2B,IAAI;AAAA,gBACpD;AAAA,cACF;AAAA,YACF,OAAO;AACL,6BAAe,eAAe,KAAK;AACnC,kBAAI,MAAM,iBAAkB;AAG5B,6BAAe,2BAA2B,IAAI;AAAA,YAChD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,gBAAgB,eAAe,UAAU,YAAY;AAC3D,cAAI,MAAM,YAAa,iBAAiB,MAAM,QAAQ,IAAM;AAC5D,cAAI,cAAc,YAAY,GAAG,EAAE,SAAS,MAAM,GAAG,GAAG;AACtD,oBAAQ,aAAa,IAAI;AAGzB,oBAAQ,SAAS,MAAM;AAEvB,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA;AAAA,IACH,GACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,mBAAmB;AAezB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;AACtE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,gBAAgB,IAAI;AACtE,UAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,UAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;AACtE,UAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;AACxE,UAAM,MAAY,aAA8B,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,WACE,oBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,aAChC,8BAAC,YAAS,SAAS,cAAc,QAAQ,MACvC,8BAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,aAC5B;AAAA,MAAC;AAAA;AAAA,QACC,IAAI,WAAW;AAAA,QACf,mBAAiB,WAAW;AAAA,QAC3B,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAM;AAAA,QACN,MAAM,YAAY,QAAQ,QAAQ,SAAS;AAAA,QAC3C,6BAA6B;AAAA,QAC7B,sBAAsB;AAAA,QACtB,WAAW;AAAA,QACX,iBAAiB,CAAC,UAAU;AAE1B,cAAI,YAAY,mBAAmB,QAAS,KAAI,SAAS,MAAM;AAC/D,gBAAM,eAAe;AAAA,QACvB;AAAA,QAGA,kBAAkB,CAAC,UAAU,MAAM,eAAe;AAAA,QAClD,gBAAgB,qBAAqB,MAAM,gBAAgB,CAAC,UAAU;AAGpE,cAAI,MAAM,WAAW,WAAW,QAAS,SAAQ,aAAa,KAAK;AAAA,QACrE,CAAC;AAAA,QACD,iBAAiB,qBAAqB,MAAM,iBAAiB,CAAC,UAAU;AACtE,sBAAY,QAAQ;AAEpB,gBAAM,eAAe;AAAA,QACvB,CAAC;AAAA,QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAE1D,gBAAM,kBAAkB,MAAM,cAAc,SAAS,MAAM,MAAqB;AAChF,gBAAM,aAAa,eAAe,YAAY,GAAG,EAAE,SAAS,MAAM,GAAG;AACrE,cAAI,mBAAmB,YAAY;AACjC,oBAAQ,aAAa,KAAK;AAE1B,uBAAW,SAAS,MAAM;AAE1B,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA;AAAA,IACH,GACF,GACF,GACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,SAAS,aAAa,MAAe;AACnC,SAAO,OAAO,SAAS;AACzB;AAEA,SAAS,gBAAgB,SAAoD;AAC3E,SAAO,YAAY;AACrB;AAEA,SAAS,gBAAgB,SAAuB;AAC9C,SAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AAEA,SAAS,WAAW,YAA2B;AAC7C,QAAM,6BAA6B,SAAS;AAC5C,aAAW,aAAa,YAAY;AAElC,QAAI,cAAc,2BAA4B;AAC9C,cAAU,MAAM;AAChB,QAAI,SAAS,kBAAkB,2BAA4B;AAAA,EAC7D;AACF;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAO,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAE;AAC/E;AAmBA,SAAS,aAAa,QAAkB,QAAgB,cAAuB;AAC7E,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAC7F,QAAM,mBAAmB,aAAa,OAAO,CAAC,IAAK;AACnD,QAAM,oBAAoB,eAAe,OAAO,QAAQ,YAAY,IAAI;AACxE,MAAI,gBAAgB,UAAU,QAAQ,KAAK,IAAI,mBAAmB,CAAC,CAAC;AACpE,QAAM,sBAAsB,iBAAiB,WAAW;AACxD,MAAI,oBAAqB,iBAAgB,cAAc,OAAO,CAAC,MAAM,MAAM,YAAY;AACvF,QAAM,YAAY,cAAc;AAAA,IAAK,CAAC,UACpC,MAAM,YAAY,EAAE,WAAW,iBAAiB,YAAY,CAAC;AAAA,EAC/D;AACA,SAAO,cAAc,eAAe,YAAY;AAClD;AASA,SAAS,iBAAiB,OAAc,SAAkB;AACxD,QAAM,EAAE,GAAG,EAAE,IAAI;AACjB,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,IAAI,QAAQ,QAAQ,IAAI,KAAK;AACnE,UAAM,KAAK,QAAQ,CAAC;AACpB,UAAM,KAAK,QAAQ,CAAC;AACpB,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AAGd,UAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AACrF,QAAI,UAAW,UAAS,CAAC;AAAA,EAC3B;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,OAA2B,MAAgB;AACvE,MAAI,CAAC,KAAM,QAAO;AAClB,QAAM,YAAY,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AACvD,SAAO,iBAAiB,WAAW,IAAI;AACzC;AAEA,SAAS,UAAa,SAAqE;AACzF,SAAO,CAAC,UAAW,MAAM,gBAAgB,UAAU,QAAQ,KAAK,IAAI;AACtE;AAEA,IAAMA,QAAO;AACb,IAAMC,UAAS;AACf,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAMC,QAAO;AACb,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,YAAY;AAClB,IAAMC,SAAQ;AACd,IAAM,MAAM;AACZ,IAAM,aAAa;AACnB,IAAM,aAAa;", "names": ["Root", "<PERSON><PERSON>", "Content", "<PERSON><PERSON>", "Arrow"]}