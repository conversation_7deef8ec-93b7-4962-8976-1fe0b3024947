// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and role management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String?
  role      UserRole @default(STUDENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  accounts    Account[]
  sessions    Session[]
  submissions TestSubmission[]
  progress    UserProgress[]
  createdTests Test[] @relation("TestCreator")

  @@map("users")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Test and Question models
model Test {
  id          String     @id @default(cuid())
  title       String
  description String?
  type        TestType
  difficulty  Difficulty @default(INTERMEDIATE)
  duration    Int        // Duration in minutes
  isActive    Boolean    @default(true)
  createdBy   String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  creator     User             @relation("TestCreator", fields: [createdBy], references: [id])
  questions   Question[]
  submissions TestSubmission[]

  @@map("tests")
}

model Question {
  id            String       @id @default(cuid())
  testId        String
  type          QuestionType
  content       String       @db.Text
  options       Json?        // For multiple choice questions
  correctAnswer Json         // Flexible to handle different answer types
  points        Int          @default(1)
  order         Int
  audioUrl      String?      // For listening questions
  imageUrl      String?      // For visual questions
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  test    Test             @relation(fields: [testId], references: [id], onDelete: Cascade)
  answers SubmissionAnswer[]

  @@map("questions")
}

// Submission models
model TestSubmission {
  id          String           @id @default(cuid())
  userId      String
  testId      String
  startedAt   DateTime         @default(now())
  completedAt DateTime?
  score       Float?
  totalPoints Int?
  status      SubmissionStatus @default(IN_PROGRESS)

  // Relations
  user    User               @relation(fields: [userId], references: [id])
  test    Test               @relation(fields: [testId], references: [id])
  answers SubmissionAnswer[]

  @@map("test_submissions")
}

model SubmissionAnswer {
  id           String @id @default(cuid())
  submissionId String
  questionId   String
  answer       Json   // Flexible to handle different answer types
  isCorrect    Boolean?
  points       Float?

  // Relations
  submission TestSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  question   Question       @relation(fields: [questionId], references: [id])

  @@unique([submissionId, questionId])
  @@map("submission_answers")
}

// Progress tracking
model UserProgress {
  id           String   @id @default(cuid())
  userId       String
  testType     TestType
  totalTests   Int      @default(0)
  completedTests Int    @default(0)
  averageScore Float?
  bestScore    Float?
  lastTestDate DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@unique([userId, testType])
  @@map("user_progress")
}

// Enums
enum UserRole {
  STUDENT
  TEACHER
  ADMIN
}

enum TestType {
  WRITING
  READING
  LISTENING
  SPEAKING
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  FILL_IN_BLANK
  ESSAY
  SHORT_ANSWER
  MATCHING
  ORDERING
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum SubmissionStatus {
  IN_PROGRESS
  COMPLETED
  ABANDONED
}
