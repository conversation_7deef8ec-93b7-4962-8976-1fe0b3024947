"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronLeft, ChevronRight, Wifi, Bell, Menu } from "lucide-react"

interface Question {
  id: number
  type: 'TRUE_FALSE_NOT_GIVEN' | 'MULTIPLE_CHOICE' | 'FILL_IN_BLANK' | 'SHORT_ANSWER'
  text: string
  options?: string[]
}

interface ReadingPart {
  partNumber: number
  title: string
  instructions: string
  passage: string
  questions: Question[]
  questionRange: string
}

interface InspecraReadingInterfaceProps {
  testTakerId: string
  parts: ReadingPart[]
  answers: Record<string, any>
  onAnswerChange: (questionId: string, answer: any) => void
  onSubmit: () => void
}

export function InspecraReadingInterface({
  testTakerId,
  parts,
  answers,
  onAnswerChange,
  onSubmit
}: InspecraReadingInterfaceProps) {
  const [currentPart, setCurrentPart] = useState(0)
  const [currentQuestionPage, setCurrentQuestionPage] = useState(0)
  
  const questionsPerPage = 6
  const currentPartData = parts[currentPart]
  const totalQuestions = currentPartData.questions.length
  const totalPages = Math.ceil(totalQuestions / questionsPerPage)
  
  const startQuestionIndex = currentQuestionPage * questionsPerPage
  const endQuestionIndex = Math.min(startQuestionIndex + questionsPerPage, totalQuestions)
  const currentQuestions = currentPartData.questions.slice(startQuestionIndex, endQuestionIndex)

  const renderQuestion = (question: Question) => {
    const answer = answers[question.id.toString()]

    switch (question.type) {
      case 'TRUE_FALSE_NOT_GIVEN':
        return (
          <div className="space-y-3">
            <p className="text-sm leading-relaxed">{question.text}</p>
            <RadioGroup
              value={answer || ''}
              onValueChange={(value) => onAnswerChange(question.id.toString(), value)}
              className="space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="TRUE" id={`${question.id}-true`} className="w-4 h-4" />
                <Label htmlFor={`${question.id}-true`} className="text-sm font-normal">TRUE</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="FALSE" id={`${question.id}-false`} className="w-4 h-4" />
                <Label htmlFor={`${question.id}-false`} className="text-sm font-normal">FALSE</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="NOT GIVEN" id={`${question.id}-not-given`} className="w-4 h-4" />
                <Label htmlFor={`${question.id}-not-given`} className="text-sm font-normal">NOT GIVEN</Label>
              </div>
            </RadioGroup>
          </div>
        )

      case 'MULTIPLE_CHOICE':
        return (
          <div className="space-y-3">
            <p className="text-sm leading-relaxed">{question.text}</p>
            <RadioGroup
              value={answer || ''}
              onValueChange={(value) => onAnswerChange(question.id.toString(), value)}
              className="space-y-2"
            >
              {question.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`${question.id}-${index}`} className="w-4 h-4" />
                  <Label htmlFor={`${question.id}-${index}`} className="text-sm font-normal">{option}</Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )

      case 'FILL_IN_BLANK':
      case 'SHORT_ANSWER':
        return (
          <div className="space-y-3">
            <p className="text-sm leading-relaxed">{question.text}</p>
            <Input
              value={answer || ''}
              onChange={(e) => onAnswerChange(question.id.toString(), e.target.value)}
              className="max-w-xs h-8 text-sm"
              placeholder="Type your answer"
            />
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="text-red-600 font-bold text-xl">IELTS</div>
          <div className="text-sm text-gray-600">Test taker ID</div>
        </div>
        <div className="flex items-center space-x-4 text-gray-600">
          <Wifi className="w-4 h-4" />
          <Bell className="w-4 h-4" />
          <Menu className="w-4 h-4" />
        </div>
      </div>

      {/* Part Header */}
      <div className="bg-gray-100 px-6 py-4 border-b border-gray-200">
        <h2 className="font-semibold text-gray-900">Part {currentPartData.partNumber}</h2>
        <p className="text-sm text-gray-700 mt-1">{currentPartData.instructions}</p>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Passage Section */}
        <div className="w-1/2 border-r border-gray-200">
          <div className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">{currentPartData.title}</h3>
            <ScrollArea className="h-[calc(100vh-280px)]">
              <div className="prose prose-sm max-w-none">
                {currentPartData.passage.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-4 text-sm leading-relaxed text-gray-800">
                    {paragraph}
                  </p>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Questions Section */}
        <div className="w-1/2">
          <div className="p-6">
            <div className="mb-4">
              <h3 className="font-semibold text-gray-900">Questions {currentPartData.questionRange}</h3>
              <p className="text-sm text-gray-600 mt-2">
                Choose <strong>TRUE</strong> if the statement agrees with the information given in the text, choose <strong>FALSE</strong> if 
                the statement contradicts the information, or choose <strong>NOT GIVEN</strong> if there is no information on 
                this.
              </p>
            </div>

            <ScrollArea className="h-[calc(100vh-320px)]">
              <div className="space-y-6">
                {currentQuestions.map((question) => (
                  <div key={question.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                    <div className="flex items-start space-x-3">
                      <div className="font-semibold text-gray-900 mt-1 min-w-[20px]">
                        {question.id}
                      </div>
                      <div className="flex-1">
                        {renderQuestion(question)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="bg-white border-t border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Part Navigation */}
          <div className="flex items-center space-x-4">
            {parts.map((part, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentPart(index)
                  setCurrentQuestionPage(0)
                }}
                className={`px-3 py-1 text-sm rounded ${
                  currentPart === index
                    ? 'bg-blue-100 text-blue-700 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                Part {part.partNumber}
              </button>
            ))}
          </div>

          {/* Question Page Navigation */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {Array.from({ length: totalPages }, (_, i) => (
                <button
                  key={i}
                  onClick={() => setCurrentQuestionPage(i)}
                  className={`w-6 h-6 text-xs rounded ${
                    currentQuestionPage === i
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {i + 1}
                </button>
              ))}
            </div>

            <div className="text-sm text-gray-500">
              Part {currentPartData.partNumber} &nbsp;&nbsp; {startQuestionIndex + 1} of {totalQuestions}
            </div>

            <div className="text-sm text-gray-500">
              Part 3 &nbsp;&nbsp; 0 of 14
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (currentQuestionPage > 0) {
                    setCurrentQuestionPage(currentQuestionPage - 1)
                  } else if (currentPart > 0) {
                    setCurrentPart(currentPart - 1)
                    setCurrentQuestionPage(Math.ceil(parts[currentPart - 1].questions.length / questionsPerPage) - 1)
                  }
                }}
                disabled={currentPart === 0 && currentQuestionPage === 0}
                className="w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (currentQuestionPage < totalPages - 1) {
                    setCurrentQuestionPage(currentQuestionPage + 1)
                  } else if (currentPart < parts.length - 1) {
                    setCurrentPart(currentPart + 1)
                    setCurrentQuestionPage(0)
                  } else {
                    onSubmit()
                  }
                }}
                className="w-8 h-8 p-0 bg-gray-600 text-white hover:bg-gray-700"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
